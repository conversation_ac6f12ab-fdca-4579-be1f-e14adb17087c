package tndung.vnfb.smm.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

@Data
public class ApiProviderReq {


    @NotBlank
    private String url;

    @NotBlank(groups = OnCreate.class)
    private String secretKey;

    private BigDecimal currencyRate = BigDecimal.ONE;
    private BigDecimal balanceAlert = BigDecimal.ZERO;
    private Boolean isActive = true;


}
