import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';
import { AdminServiceService } from '../../../core/services/admin-service.service';
import { ProviderRes } from '../../../model/response/provider-res.model';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { IconsModule } from '../../../icons/icons.module';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-add-provider',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconsModule,
    TranslateModule,
    FaIconComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './add-provider.component.html',
  styleUrl: './add-provider.component.css'
})
export class AddProviderComponent implements OnInit {
  @Input() provider: ProviderRes | null = null;
  @Input() changeKeyMode: boolean = false;
  @Output() close = new EventEmitter<void>();
  @Output() providerAdded = new EventEmitter<ProviderRes>();

  // Form fields
  url: string = '';
  secretKey: string = '';
  name: string = '';
  balanceAlert: number = 0;
  currencyRate: number = 1;
  enableCurrencyRate: boolean = false;

  isEdit: boolean = false;
  isLoading: boolean = false;
  errorMessage: string = '';

  constructor(private adminService: AdminServiceService) {}

  ngOnInit(): void {
    if (this.provider) {
      this.isEdit = true;
      this.url = this.provider.url;
      this.secretKey = this.provider.secret_key;
      this.name = this.provider.name;
      this.balanceAlert = parseFloat(this.provider.balance_alert) || 0;
      this.currencyRate = this.provider.currency_rate || 1;
      this.enableCurrencyRate = this.provider.currency_rate !== 1;
    }
  }

  closePopup(): void {
    this.close.emit();
  }

  saveProvider(): void {
    this.isLoading = true;
    this.errorMessage = '';

    if (this.changeKeyMode && this.provider) {
      // Change API key mode
      this.adminService.changeProviderApiKey(this.provider.id, this.secretKey)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: () => {
            // Update the provider object with the new API key
            if (this.provider) {
              const updatedProvider: ProviderRes = {
                ...this.provider,
                secret_key: this.secretKey
              };
              this.providerAdded.emit(updatedProvider);
              this.closePopup();
            }
          },
          error: (error) => {
            console.error('Error changing API key:', error);
            this.errorMessage = 'Failed to change API key. Please try again.';
          }
        });
    } else if (this.isEdit && this.provider) {
      // Edit provider mode
      const currencyRate = this.enableCurrencyRate ? this.currencyRate : 1;
      this.adminService.editProvider(
        this.provider.id,
        this.url,
        this.name,
        this.balanceAlert,
        this.secretKey,
        currencyRate
      )
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: (provider) => {
            this.providerAdded.emit(provider);
            this.closePopup();
          },
          error: (error) => {
            console.error('Error editing provider:', error);
            this.errorMessage = 'Failed to edit provider. Please try again.';
          }
        });
    } else {
      // Add new provider mode
      const currencyRate = this.enableCurrencyRate ? this.currencyRate : 1;
      this.adminService.addProvider(this.url, this.secretKey, currencyRate)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe({
          next: (provider) => {
            this.providerAdded.emit(provider);
            this.closePopup();
          },
          error: (error) => {
            console.error('Error adding provider:', error);
            this.errorMessage = 'Failed to add provider. Please check the URL and API key.';
          }
        });
    }
  }
}
